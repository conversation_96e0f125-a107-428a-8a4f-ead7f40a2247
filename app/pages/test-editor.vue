<template>
  <UContainer class="py-8">
    <h1 class="text-2xl font-bold mb-6">Тест редактора с LinkCard</h1>

    <div class="space-y-6">
      <div>
        <h2 class="text-lg font-semibold mb-2">Редактор:</h2>
        <Editor v-model="content" placeholder="Введите текст или добавьте LinkCard..." />
        <div class="mt-2">
          <UButton @click="testLinkCard" size="sm" color="primary">
            Добавить тестовую LinkCard
          </UButton>
        </div>
      </div>

      <div>
        <h2 class="text-lg font-semibold mb-2">JSON вывод:</h2>
        <pre class="bg-gray-100 dark:bg-gray-800 p-4 rounded-lg text-sm overflow-auto">{{ JSON.stringify(content, null, 2) }}</pre>
      </div>
    </div>
  </UContainer>
</template>

<script setup lang="ts">
const content = ref('')

const testLinkCard = () => {
  // Добавляем тестовую LinkCard напрямую в JSON
  const testCard = {
    type: 'doc',
    content: [
      {
        type: 'linkCard',
        attrs: {
          url: 'https://example.com',
          title: 'Пример карточки-ссылки',
          image: 'https://via.placeholder.com/150x150'
        }
      }
    ]
  }
  content.value = testCard
}
</script>
