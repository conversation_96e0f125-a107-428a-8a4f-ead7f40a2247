import { Node, mergeAttributes } from '@tiptap/core'
import { VueNodeViewRenderer } from '@tiptap/vue-3'
import { defineComponent, h } from 'vue'

export interface LinkCardOptions {
  HTMLAttributes: Record<string, any>
}

declare module '@tiptap/core' {
  interface Commands<ReturnType> {
    linkCard: {
      setLinkCard: (options: { url: string; title?: string; image?: string }) => ReturnType
    }
  }
}

export const LinkCard = Node.create<LinkCardOptions>({
  name: 'linkCard',

  group: 'block',

  atom: true,

  addOptions() {
    return {
      HTMLAttributes: {},
    }
  },

  addAttributes() {
    return {
      url: {
        default: null,
        parseHTML: element => element.getAttribute('data-url'),
        renderHTML: attributes => {
          if (!attributes.url) {
            return {}
          }
          return {
            'data-url': attributes.url,
          }
        },
      },
      title: {
        default: null,
        parseHTML: element => element.getAttribute('data-title'),
        renderHTML: attributes => {
          if (!attributes.title) {
            return {}
          }
          return {
            'data-title': attributes.title,
          }
        },
      },
      image: {
        default: null,
        parseHTML: element => element.getAttribute('data-image'),
        renderHTML: attributes => {
          if (!attributes.image) {
            return {}
          }
          return {
            'data-image': attributes.image,
          }
        },
      },
    }
  },

  parseHTML() {
    return [
      {
        tag: 'div[data-type="linkCard"]',
      },
    ]
  },

  renderHTML({ HTMLAttributes }) {
    return ['div', mergeAttributes(HTMLAttributes, { 'data-type': 'linkCard' }), 0]
  },

  addNodeView() {
    return VueNodeViewRenderer(LinkCardNodeView)
  },

  addCommands() {
    return {
      setLinkCard: options => ({ commands }) => {
        return commands.insertContent({
          type: this.name,
          attrs: options,
        })
      },
    }
  },
})

// Vue компонент для отображения LinkCard в редакторе
const LinkCardNodeView = defineComponent({
  props: {
    node: {
      type: Object,
      required: true,
    },
    updateAttributes: {
      type: Function,
      required: true,
    },
    deleteNode: {
      type: Function,
      required: true,
    },
  },
  setup(props) {
    return () => h('div', {
      class: 'link-card-node border border-gray-200 dark:border-gray-700 rounded-lg p-4 my-4 bg-white dark:bg-gray-800',
      contenteditable: false,
    }, [
      h('div', { class: 'flex items-start gap-3' }, [
        props.node.attrs.image && h('img', {
          src: props.node.attrs.image,
          alt: props.node.attrs.title || 'Link preview',
          class: 'w-16 h-16 object-cover rounded-md flex-shrink-0',
        }),
        h('div', { class: 'flex-1 min-w-0' }, [
          h('h3', {
            class: 'font-medium text-sm text-gray-900 dark:text-gray-100 truncate',
          }, props.node.attrs.title || 'Без заголовка'),
          h('p', {
            class: 'text-xs text-gray-500 dark:text-gray-400 mt-1 truncate',
          }, props.node.attrs.url || ''),
        ]),
        h('button', {
          class: 'text-gray-400 hover:text-red-500 p-1',
          onClick: props.deleteNode,
        }, '×'),
      ]),
    ])
  },
})
