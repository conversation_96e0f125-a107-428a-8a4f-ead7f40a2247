interface LinkPreviewData {
  url: string
  title: string
  description: string
  image: string | null
}

interface LinkPreviewResponse {
  success: boolean
  data: LinkPreviewData
}

export const useLinkPreview = () => {
  const isLoading = ref(false)
  const error = ref<string | null>(null)

  const fetchLinkPreview = async (url: string): Promise<LinkPreviewData | null> => {
    if (!url) {
      error.value = 'URL не может быть пустым'
      return null
    }

    // Проверяем формат URL
    try {
      new URL(url)
    } catch {
      error.value = 'Неверный формат URL'
      return null
    }

    isLoading.value = true
    error.value = null

    try {
      const response = await $fetch<LinkPreviewResponse>('/api/link-preview', {
        query: { url }
      })

      if (response.success) {
        return response.data
      } else {
        error.value = 'Не удалось получить данные по ссылке'
        return null
      }
    } catch (err: any) {
      console.error('Link preview error:', err)
      error.value = err.data?.message || 'Ошибка при получении данных по ссылке'
      return null
    } finally {
      isLoading.value = false
    }
  }

  return {
    fetchLinkPreview,
    isLoading: readonly(isLoading),
    error: readonly(error)
  }
}
