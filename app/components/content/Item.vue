<template>
  <NuxtLink
    :to="{ name: 'content-slug', params: { slug: item.slug } }"
    class="flex w-full flex-col"
  >
    <img :src="item.image" class="h-68 md:h-42 w-full" />
    <span class="mt-2 space-y-2 px-0.5">
      <span
        class="inline h-10 text-lg font-semibold md:line-clamp-2 md:text-sm"
      >
        {{ item.title }}
      </span>
      <span
        class="text-md mb-auto line-clamp-4 text-[var(--ui-text-muted)] md:line-clamp-2 md:text-sm"
      >
        {{ item.description }}
      </span>
      <span
        class="flex items-center gap-1 text-sm text-[var(--ui-text-muted)] md:text-xs mt-2"
      >
        <UIcon name="i-lucide-clock" />
        <NuxtTime :datetime="item.published_at" date-style="medium" time-style="short" />
      </span>
    </span>
  </NuxtLink>
</template>

<script setup lang="ts">
const props = defineProps({
  item: {
    type: Object,
    required: true
  },
});
</script>
