<template>
  <a
    :href="url"
    target="_blank"
    rel="noopener noreferrer"
    class="block border border-gray-200 dark:border-gray-700 rounded-lg p-4 hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors duration-200 no-underline"
  >
    <div class="flex items-start gap-4">
      <!-- Изображение -->
      <div v-if="image" class="flex-shrink-0">
        <img
          :src="image"
          :alt="title || 'Link preview'"
          class="w-20 h-20 object-cover rounded-md"
        />
      </div>
      
      <!-- Контент -->
      <div class="flex-1 min-w-0">
        <!-- Заголовок -->
        <h3 class="font-medium text-gray-900 dark:text-gray-100 mb-2 line-clamp-2">
          {{ title || 'Без заголовка' }}
        </h3>
        
        <!-- URL -->
        <p class="text-sm text-gray-500 dark:text-gray-400 truncate">
          {{ displayUrl }}
        </p>
      </div>
      
      <!-- Иконка внешней ссылки -->
      <div class="flex-shrink-0">
        <UIcon 
          name="i-lucide-external-link" 
          class="w-4 h-4 text-gray-400 dark:text-gray-500"
        />
      </div>
    </div>
  </a>
</template>

<script setup lang="ts">
interface Props {
  url: string
  title?: string
  image?: string
}

const props = defineProps<Props>()

// Получаем читаемый URL для отображения
const displayUrl = computed(() => {
  try {
    const urlObj = new URL(props.url)
    return urlObj.hostname
  } catch {
    return props.url
  }
})
</script>

<style scoped>
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style>
