<template>
  <UModal v-model="isOpen" :ui="{ width: 'sm:max-w-md' }">
    <UCard>
      <template #header>
        <div class="flex items-center justify-between">
          <h3 class="text-lg font-semibold">Добавить карточку-ссылку</h3>
          <UButton
            color="gray"
            variant="ghost"
            icon="i-heroicons-x-mark-20-solid"
            class="-my-1"
            @click="isOpen = false"
          />
        </div>
      </template>

      <div class="space-y-4">
        <UFormGroup label="URL ссылки" required>
          <UInput
            v-model="url"
            placeholder="https://example.com"
            :disabled="isLoading"
            @keyup.enter="handleSubmit"
          />
        </UFormGroup>

        <!-- Предпросмотр -->
        <div v-if="preview" class="border border-gray-200 dark:border-gray-700 rounded-lg p-3">
          <div class="flex items-start gap-3">
            <img
              v-if="preview.image"
              :src="preview.image"
              :alt="preview.title"
              class="w-16 h-16 object-cover rounded-md flex-shrink-0"
            />
            <div class="flex-1 min-w-0">
              <h4 class="font-medium text-sm text-gray-900 dark:text-gray-100 line-clamp-2">
                {{ preview.title || 'Без заголовка' }}
              </h4>
              <p class="text-xs text-gray-500 dark:text-gray-400 mt-1 truncate">
                {{ preview.description }}
              </p>
            </div>
          </div>
        </div>

        <!-- Ошибка -->
        <UAlert
          v-if="error"
          color="red"
          variant="soft"
          :title="error"
          :close-button="{ icon: 'i-heroicons-x-mark-20-solid', color: 'red', variant: 'link' }"
          @close="error = null"
        />
      </div>

      <template #footer>
        <div class="flex justify-end gap-2">
          <UButton color="gray" variant="ghost" @click="isOpen = false">
            Отмена
          </UButton>
          <UButton
            color="primary"
            :loading="isLoading"
            :disabled="!url"
            @click="handleSubmit"
          >
            Добавить
          </UButton>
        </div>
      </template>
    </UCard>
  </UModal>
</template>

<script setup lang="ts">
interface LinkPreviewData {
  url: string
  title: string
  description: string
  image: string | null
}

interface Props {
  modelValue: boolean
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void
  (e: 'submit', data: LinkPreviewData): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const isOpen = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const url = ref('')
const preview = ref<LinkPreviewData | null>(null)
const error = ref<string | null>(null)
const isLoading = ref(false)

const { fetchLinkPreview } = useLinkPreview()

// Автоматически загружаем предпросмотр при изменении URL
const debouncedUrl = refDebounced(url, 1000)

watch(debouncedUrl, async (newUrl) => {
  if (!newUrl || !isValidUrl(newUrl)) {
    preview.value = null
    return
  }

  isLoading.value = true
  error.value = null

  try {
    const metadata = await fetchLinkPreview(newUrl)
    if (metadata) {
      preview.value = metadata
    }
  } catch (err: any) {
    error.value = err.message || 'Ошибка при загрузке предпросмотра'
  } finally {
    isLoading.value = false
  }
})

const isValidUrl = (string: string): boolean => {
  try {
    new URL(string)
    return true
  } catch {
    return false
  }
}

const handleSubmit = async () => {
  if (!url.value) return

  if (!isValidUrl(url.value)) {
    error.value = 'Неверный формат URL'
    return
  }

  // Если у нас уже есть предпросмотр, используем его
  if (preview.value) {
    emit('submit', preview.value)
    isOpen.value = false
    resetForm()
    return
  }

  // Иначе загружаем данные
  isLoading.value = true
  error.value = null

  try {
    const metadata = await fetchLinkPreview(url.value)
    if (metadata) {
      emit('submit', metadata)
      isOpen.value = false
      resetForm()
    } else {
      // Если не удалось получить метаданные, создаем базовую карточку
      emit('submit', {
        url: url.value,
        title: '',
        description: '',
        image: null
      })
      isOpen.value = false
      resetForm()
    }
  } catch (err: any) {
    error.value = err.message || 'Ошибка при создании карточки'
  } finally {
    isLoading.value = false
  }
}

const resetForm = () => {
  url.value = ''
  preview.value = null
  error.value = null
  isLoading.value = false
}

// Сбрасываем форму при закрытии модального окна
watch(isOpen, (newValue) => {
  if (!newValue) {
    resetForm()
  }
})
</script>

<style scoped>
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style>
