// Базовые типы для блоков контента
export interface ContentBlockBase {
  id: number;
  content_id: number;
  type: string;
  position: number;
  created_at: string;
  updated_at: string;
}

// Типы данных для разных блоков
export interface TextBlockData {
  content: string;
}

export interface ImageBlockData {
  url: string;
  alt: string;
  caption?: string;
}

export interface QuoteBlockData {
  text: string;
  author?: string;
}

export interface VideoBlockData {
  url: string;
  title?: string;
}

export interface GalleryImage {
  url: string;
  alt: string;
}

export interface GalleryBlockData {
  images: GalleryImage[];
}

export interface EmbedBlockData {
  code: string;
}

export interface LinkCardBlockData {
  url: string;
  title?: string;
  image?: string;
}

// Специфичные типы блоков
export interface TextBlock extends ContentBlockBase {
  type: 'text';
  data: TextBlockData;
}

export interface ImageBlock extends ContentBlockBase {
  type: 'image';
  data: ImageBlockData;
}

export interface QuoteBlock extends ContentBlockBase {
  type: 'quote';
  data: QuoteBlockData;
}

export interface VideoBlock extends ContentBlockBase {
  type: 'video';
  data: VideoBlockData;
}

export interface GalleryBlock extends ContentBlockBase {
  type: 'gallery';
  data: GalleryBlockData;
}

export interface EmbedBlock extends ContentBlockBase {
  type: 'embed';
  data: EmbedBlockData;
}

export interface LinkCardBlock extends ContentBlockBase {
  type: 'linkCard';
  data: LinkCardBlockData;
}

// Объединенный тип для всех блоков
export type ContentBlock = TextBlock | ImageBlock | QuoteBlock | VideoBlock | GalleryBlock | EmbedBlock | LinkCardBlock;

// Тип для категории контента
export interface ContentCategory {
  id: number;
  name: string;
  slug: string;
  description: string;
  image?: string;
  meta_title: string;
  meta_description: string;
  meta_keywords: string;
  created_at: string;
  updated_at: string;
}

// Основной тип для элемента контента
export interface ContentItem {
  id: number;
  content_category_id: number;
  title: string;
  subtitle?: string;
  slug: string;
  description: string;
  content: string;
  image_alt?: string;
  published_at: string;
  status: string;
  meta_title: string;
  meta_description: string;
  meta_keywords: string;
  settings?: any;
  type?: string;
  created_at: string;
  updated_at: string;
  category: ContentCategory;
  blocks: ContentBlock[];
}

// Тип ответа API для одного элемента контента
export interface ContentSingleResponse {
  data: ContentItem;
}

// Тип ответа API для списка контента
export interface ContentResponse {
  data: ContentItem[];
  meta: {
    current_page: number;
    from: number;
    last_page: number;
    per_page: number;
    to: number;
    total: number;
  };
}
