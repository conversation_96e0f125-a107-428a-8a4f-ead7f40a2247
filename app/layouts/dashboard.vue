<script setup lang="ts">
import type { User } from "~/types/auth";

interface NavigationItem {
  label: string;
  icon?: string;
  to?: string;
  badge?: number;
  children?: NavigationItem[];
}

const user = useSanctumUser<User>();
const links = computed(() => {
  const items: NavigationItem[][] = [
    [
      {
        label: "Главная",
        icon: "i-lucide-house",
        to: "/"
      },
      {
        label: "Мои объявления",
        icon: "i-lucide-layout-list",
        to: "/profile"
      },
      {
        label: "Избранное",
        to: "/favorites",
        badge: user.value?.favorites,
        icon: "i-lucide-star"
      },
      {
        label: "Сообщения",
        icon: "i-lucide-inbox",
        to: "/inbox",
        badge: user.value?.messages
      },
      {
        label: "Уведомления",
        icon: "i-lucide-bell",
        to: "/notifications",
        badge: user.value?.notifications
      },
      // {
      //   label: "Кошелек",
      //   icon: "i-lucide-wallet",
      //   badge: user.value.balance,
      //   to: "/wallet"
      // },
      {
        label: "Настройки",
        to: "/settings",
        icon: "i-lucide-settings"
      }
    ],
    [
      {
        label: "Поддержка",
        to: `/inbox/support`
      }
    ]
  ];
  if (user.value?.role === "admin") {
    items[1].push(
      {
        label: "Модерация",
        icon: "i-lucide-ethernet-port",
        to: "/admin/moderations"
      },
      {
        label: "Жалобы",
        icon: "i-lucide-flag-triangle-right",
        to: "/admin/reports"
      },
      {
        label: "SEO Страницы",
        icon: "i-lucide-table-of-contents",
        to: "/admin/seo"
      },
      {
        label: "Статьи",
        icon: "i-lucide-newspaper",
        to: "/admin/content"
      },
      {
        label: "Новости",
        icon: "i-lucide-newspaper",
        to: "/admin/news"
      },
      {
        label: "События",
        icon: "i-lucide-calendar-1",
        to: "/admin/events"
      }
    );
  }

  return items;
});
</script>

<template>
  <UDashboardGroup>
    <UDashboardSidebar id="default" collapsible :min-size="22" class="bg-(--ui-bg-elevated)/25">
      <template #header="{ collapsed }">
        <NuxtLink to="/" class="flex items-end gap-0.5">
          <LogoCollapsed
            class="h-6 w-auto shrink-0"
            :class="[collapsed ? 'ml-1' : 'ml-2']"
            :collapsed="collapsed"
          />
        </NuxtLink>

        <div v-if="!collapsed" class="ms-auto flex items-center gap-1.5">
          <UDashboardSidebarCollapse />
        </div>
      </template>

      <template #default="{ collapsed }">
        <UDashboardSidebarCollapse v-if="collapsed" />
        <UNavigationMenu :collapsed="collapsed" :items="links[0]" orientation="vertical" />

        <UNavigationMenu
          :collapsed="collapsed"
          :items="links[1]"
          orientation="vertical"
          class="mt-auto"
        />
      </template>
    </UDashboardSidebar>

    <slot />

    <!--    <NotificationsSlideover /> -->
  </UDashboardGroup>
</template>
