export default defineEventHandler(async (event) => {
  const query = getQuery(event)
  const url = query.url as string

  if (!url) {
    throw createError({
      statusCode: 400,
      statusMessage: 'URL parameter is required'
    })
  }

  try {
    // Проверяем, что URL валидный
    new URL(url)
  } catch {
    throw createError({
      statusCode: 400,
      statusMessage: 'Invalid URL format'
    })
  }

  try {
    // Получаем HTML страницы
    const response = await fetch(url, {
      headers: {
        'User-Agent': 'Mozilla/5.0 (compatible; LinkPreviewBot/1.0; +https://gunpost.ru/)'
      },
      // Ограничиваем время ожидания
      signal: AbortSignal.timeout(10000)
    })

    if (!response.ok) {
      throw createError({
        statusCode: response.status,
        statusMessage: `Failed to fetch URL: ${response.statusText}`
      })
    }

    const html = await response.text()
    
    // Извлекаем метаданные
    const metadata = extractMetadata(html, url)
    
    return {
      success: true,
      data: metadata
    }
  } catch (error: any) {
    console.error('Link preview error:', error)
    
    throw createError({
      statusCode: 500,
      statusMessage: error.message || 'Failed to fetch link preview'
    })
  }
})

function extractMetadata(html: string, url: string) {
  // Простой парсер метаданных без использования внешних библиотек
  const getMetaContent = (property: string): string | null => {
    // Open Graph теги
    const ogRegex = new RegExp(`<meta\\s+property=["']og:${property}["']\\s+content=["']([^"']*?)["']`, 'i')
    const ogMatch = html.match(ogRegex)
    if (ogMatch) return ogMatch[1]

    // Twitter Card теги
    const twitterRegex = new RegExp(`<meta\\s+name=["']twitter:${property}["']\\s+content=["']([^"']*?)["']`, 'i')
    const twitterMatch = html.match(twitterRegex)
    if (twitterMatch) return twitterMatch[1]

    // Стандартные meta теги
    const metaRegex = new RegExp(`<meta\\s+name=["']${property}["']\\s+content=["']([^"']*?)["']`, 'i')
    const metaMatch = html.match(metaRegex)
    if (metaMatch) return metaMatch[1]

    return null
  }

  // Извлекаем title из тега <title>
  const getTitleFromTag = (): string | null => {
    const titleMatch = html.match(/<title[^>]*>([^<]*)<\/title>/i)
    return titleMatch ? titleMatch[1].trim() : null
  }

  // Получаем абсолютный URL для изображения
  const getAbsoluteUrl = (imageUrl: string | null): string | null => {
    if (!imageUrl) return null
    
    try {
      // Если URL уже абсолютный
      if (imageUrl.startsWith('http')) return imageUrl
      
      // Создаем абсолютный URL
      const baseUrl = new URL(url)
      return new URL(imageUrl, baseUrl.origin).href
    } catch {
      return null
    }
  }

  const title = getMetaContent('title') || getTitleFromTag() || ''
  const description = getMetaContent('description') || ''
  const image = getAbsoluteUrl(getMetaContent('image'))

  return {
    url,
    title: title.substring(0, 200), // Ограничиваем длину
    description: description.substring(0, 300),
    image
  }
}
